import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CheckCircle, Clock, ArrowLeft, Send, Menu, X } from 'lucide-react';
import ViaAssessment from './ViaAssessment';
import RiasecAssessment from './RiasecAssessment';
import BigFiveAssessment from './BigFiveAssessment';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';
import apiService from '../../services/apiService';
import { transformAssessmentScores } from '../../utils/assessmentTransformers';

const AssessmentFlow = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('via');
  const [assessmentData, setAssessmentData] = useState({
    via: {},
    riasec: {},
    bigFive: {}
  });
  const [completionStatus, setCompletionStatus] = useState({
    via: false,
    riasec: false,
    bigFive: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);

  // Question counts for each assessment
  const questionCounts = {
    via: 96,
    riasec: 60,
    bigFive: 44
  };
  const totalQuestions = 200;

  // Load saved progress from localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('assessmentProgress');
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setAssessmentData(parsed.data || { via: {}, riasec: {}, bigFive: {} });
        setCompletionStatus(parsed.status || { via: false, riasec: false, bigFive: false });
      } catch (error) {
        console.error('Error loading saved assessment data:', error);
      }
    }
  }, []);

  // Save progress to localStorage
  useEffect(() => {
    const progressData = {
      data: assessmentData,
      status: completionStatus,
      timestamp: Date.now()
    };
    localStorage.setItem('assessmentProgress', JSON.stringify(progressData));
  }, [assessmentData, completionStatus]);

  const handleAssessmentUpdate = (type, data, isComplete) => {
    setAssessmentData(prev => ({
      ...prev,
      [type]: data
    }));

    setCompletionStatus(prev => ({
      ...prev,
      [type]: isComplete
    }));
  };

  // Calculate overall progress
  const calculateOverallProgress = () => {
    let totalAnswered = 0;

    // Count answered questions for each assessment
    Object.entries(assessmentData).forEach(([type, data]) => {
      if (data && typeof data === 'object') {
        totalAnswered += Object.keys(data).length;
      }
    });

    return totalAnswered;
  };

  const overallProgress = calculateOverallProgress();
  const isAllComplete = completionStatus.via && completionStatus.riasec && completionStatus.bigFive;

  const handleSubmit = async () => {
    if (!isAllComplete) {
      setSubmitError('Please complete all assessments before submitting.');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Transform scores to API format
      const transformedData = transformAssessmentScores(assessmentData);
      
      // Submit to API
      const response = await apiService.submitAssessment(transformedData);
      
      // Clear saved progress
      localStorage.removeItem('assessmentProgress');
      localStorage.removeItem('viaAssessmentAnswers');
      localStorage.removeItem('riasecAssessmentAnswers');
      localStorage.removeItem('bigFiveAssessmentAnswers');

      // Navigate to status page
      navigate(`/assessment/status/${response.job_id}`);
    } catch (error) {
      console.error('Assessment submission error:', error);
      setSubmitError(error.response?.data?.message || 'Failed to submit assessment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const tabs = [
    {
      id: 'via',
      name: 'Character Strengths',
      description: 'VIA-IS Assessment',
      component: ViaAssessment
    },
    {
      id: 'riasec',
      name: 'Career Interests',
      description: 'RIASEC Holland Codes',
      component: RiasecAssessment
    },
    {
      id: 'bigFive',
      name: 'Personality Traits',
      description: 'Big Five Inventory',
      component: BigFiveAssessment
    }
  ];

  const getTabIcon = (tabId) => {
    if (completionStatus[tabId]) {
      return <CheckCircle className="w-5 h-5 text-emerald-600" />;
    }
    return <Clock className="w-5 h-5 text-slate-400" />;
  };

  return (
    <div id="assessment-flow-container" className="min-h-screen bg-slate-50">
      <div id="assessment-flow-content" className="container mx-auto px-4 py-6">
        {/* Header with Progress Overview */}
        <motion.div
          id="assessment-flow-header"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <button
            onClick={() => navigate('/dashboard')}
            className="flex items-center text-slate-600 hover:text-slate-900 mb-3 transition-colors text-sm"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </button>

          <div id="assessment-flow-title" className="text-center mb-6">
            <h1 className="text-2xl md:text-3xl font-semibold text-slate-900 mb-2">
              AI-Driven Talent Mapping Assessment
            </h1>
            <p className="text-slate-600 max-w-2xl mx-auto text-sm">
              Complete all three assessments to get comprehensive insights into your personality,
              career interests, and character strengths.
            </p>
          </div>

          {/* Progress Overview - Integrated with Header */}
          <motion.div
            id="assessment-flow-progress-overview"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-sm shadow-sm border border-slate-200 p-4"
          >
            <div id="assessment-flow-progress-header" className="flex items-center justify-between mb-4">
              <h2 className="text-base font-medium text-slate-900">Overall Progress</h2>
              <div id="assessment-flow-progress-counter" className="text-xs text-slate-600">
                {overallProgress}/{totalQuestions} questions answered
              </div>
            </div>

            {/* Overall Progress Bar */}
            <div id="assessment-flow-progress-bar-section" className="mb-4">
              <div id="assessment-flow-progress-bar-header" className="flex items-center justify-between mb-2">
                <span className="text-xs font-medium text-slate-700">Total Assessment Progress</span>
                <span className="text-xs text-slate-600">{Math.round((overallProgress / totalQuestions) * 100)}%</span>
              </div>
              <div id="assessment-flow-progress-bar-container" className="w-full bg-slate-200 rounded-sm h-2">
                <div
                  id="assessment-flow-progress-bar"
                  className="bg-slate-700 h-2 rounded-sm transition-all duration-300"
                  style={{ width: `${(overallProgress / totalQuestions) * 100}%` }}
                ></div>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Layout with Side Navigation and Main Content */}
        <div className="flex gap-6">
          {/* Side Navigation - Hidden on mobile, visible on desktop */}
          <motion.div
            id="assessment-flow-desktop-nav"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="hidden lg:block w-56 bg-white rounded-sm shadow-sm border border-slate-200 p-3 max-h-[80vh] overflow-y-auto"
          >
            <h3 className="text-xs font-medium text-slate-900 mb-3">Quick Navigation</h3>

            {/* Assessment Switcher */}
            <div id="assessment-flow-desktop-assessment-switcher" className="mb-3">
              <h4 className="text-xs font-medium text-slate-700 mb-2">Switch Assessment</h4>
              <div id="assessment-flow-desktop-assessment-buttons" className="space-y-1">
                {tabs.map((tab) => (
                  <button
                    id={`assessment-flow-desktop-tab-${tab.id}`}
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full text-left p-2 rounded-sm border text-xs transition-all ${
                      activeTab === tab.id
                        ? 'border-slate-400 bg-slate-100 text-slate-900'
                        : completionStatus[tab.id]
                        ? 'border-slate-300 bg-slate-50 text-slate-900'
                        : 'border-slate-200 bg-white text-slate-700 hover:border-slate-300'
                    }`}
                  >
                    <div id={`assessment-flow-desktop-tab-header-${tab.id}`} className="flex items-center justify-between">
                      <span className="font-medium">{tab.name}</span>
                      {getTabIcon(tab.id)}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Current Assessment Quick Nav */}
            {activeTab && (
              <div id="assessment-flow-desktop-quick-nav">
                <h4 className="text-xs font-medium text-slate-700 mb-2">
                  {tabs.find(tab => tab.id === activeTab)?.name} Categories
                </h4>
                <div id="quick-navigation-container" className="space-y-1">
                  {/* This will be populated by individual assessment components */}
                </div>
              </div>
            )}
          </motion.div>

          {/* Main Content Area - Full width for assessment content */}
          <div id="assessment-flow-main-content" className="flex-1">
            {/* Mobile Navigation Button */}
            <button
              id="assessment-flow-mobile-nav-button"
              onClick={() => setIsMobileNavOpen(!isMobileNavOpen)}
              className="lg:hidden fixed bottom-4 right-4 w-10 h-10 bg-slate-700 text-white rounded-sm shadow-sm z-20 flex items-center justify-center"
            >
              {isMobileNavOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
            </button>

            {/* Mobile Navigation Overlay */}
            {isMobileNavOpen && (
              <motion.div
                id="assessment-flow-mobile-nav-overlay"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-15"
                onClick={() => setIsMobileNavOpen(false)}
              />
            )}

            {/* Mobile Navigation Panel */}
            <motion.div
              id="assessment-flow-mobile-nav-panel"
              initial={{ x: '-100%' }}
              animate={{ x: isMobileNavOpen ? 0 : '-100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="lg:hidden fixed left-0 top-0 h-full w-72 bg-white shadow-sm z-20 p-4 overflow-y-auto"
            >
              <div id="assessment-flow-mobile-nav-header" className="flex items-center justify-between mb-4">
                <h3 className="text-base font-medium text-slate-900">Quick Navigation</h3>
                <button
                  onClick={() => setIsMobileNavOpen(false)}
                  className="p-1 text-slate-500 hover:text-slate-700"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              {/* Assessment Switcher */}
              <div id="assessment-flow-mobile-assessment-switcher" className="mb-4">
                <h4 className="text-xs font-medium text-slate-700 mb-2">Switch Assessment</h4>
                <div id="assessment-flow-mobile-assessment-buttons" className="space-y-2">
                  {tabs.map((tab) => (
                    <button
                      id={`assessment-flow-mobile-tab-${tab.id}`}
                      key={tab.id}
                      onClick={() => {
                        setActiveTab(tab.id);
                        setIsMobileNavOpen(false);
                      }}
                      className={`w-full text-left p-3 rounded-sm border transition-all ${
                        activeTab === tab.id
                          ? 'border-slate-400 bg-slate-100 text-slate-900'
                          : completionStatus[tab.id]
                          ? 'border-slate-300 bg-slate-50 text-slate-900'
                          : 'border-slate-200 bg-white text-slate-700 hover:border-slate-300'
                      }`}
                    >
                      <div id={`assessment-flow-mobile-tab-header-${tab.id}`} className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">{tab.name}</span>
                        {getTabIcon(tab.id)}
                      </div>
                      <span className="text-xs text-slate-600">{tab.description}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Current Assessment Quick Nav */}
              {activeTab && (
                <div id="assessment-flow-mobile-quick-nav">
                  <h4 className="text-sm font-medium text-slate-700 mb-3">
                    {tabs.find(tab => tab.id === activeTab)?.name} Categories
                  </h4>
                  <div id="mobile-quick-navigation-container" className="space-y-2">
                    {/* This will be populated by individual assessment components */}
                  </div>
                </div>
              )}
            </motion.div>

            {/* Assessment Content - Takes full space */}
            <motion.div
              id="assessment-flow-assessment-content"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-sm shadow-sm border border-slate-200 overflow-hidden"
            >
              {tabs.map((tab) => {
                const Component = tab.component;
                return (
                  <div
                    id={`assessment-flow-content-${tab.id}`}
                    key={tab.id}
                    className={activeTab === tab.id ? 'block' : 'hidden'}
                  >
                    <Component
                      data={assessmentData[tab.id]}
                      onUpdate={(data, isComplete) => handleAssessmentUpdate(tab.id, data, isComplete)}
                      isActive={activeTab === tab.id}
                      showQuickNavigation={false}
                    />
                  </div>
                );
              })}
            </motion.div>
          </div>
        </div>

        {/* Submit Section */}
        <motion.div
          id="assessment-flow-submit-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-6 text-center"
        >
          {submitError && (
            <div id="assessment-flow-submit-error" className="mb-4">
              <ErrorMessage message={submitError} />
            </div>
          )}

          <button
            id="assessment-flow-submit-button"
            onClick={handleSubmit}
            disabled={!isAllComplete || isSubmitting}
            className={`inline-flex items-center px-6 py-2 rounded-sm text-sm font-medium transition-all ${
              isAllComplete && !isSubmitting
                ? 'bg-slate-700 hover:bg-slate-800 text-white shadow-sm'
                : 'bg-slate-300 text-slate-500 cursor-not-allowed'
            }`}
          >
            {isSubmitting ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Submitting Assessment...
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                Submit Assessment
              </>
            )}
          </button>

          {!isAllComplete && (
            <p className="text-xs text-slate-600 mt-2">
              Complete all three assessments to submit
            </p>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default AssessmentFlow;
