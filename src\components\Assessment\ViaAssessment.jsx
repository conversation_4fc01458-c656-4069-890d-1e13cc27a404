import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { viaQuestions } from '../../data/assessmentQuestions';

const ViaAssessment = ({ data, onUpdate, isActive, showQuickNavigation = true }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState(data || {});

  // Load saved answers from localStorage on component mount
  useEffect(() => {
    const savedAnswers = localStorage.getItem('viaAssessmentAnswers');
    if (savedAnswers && Object.keys(data || {}).length === 0) {
      try {
        const parsedAnswers = JSON.parse(savedAnswers);
        setAnswers(parsedAnswers);
      } catch (error) {
        console.error('Error loading saved VIA answers:', error);
      }
    }
  }, [data]);

  // Save answers to localStorage whenever answers change
  useEffect(() => {
    if (Object.keys(answers).length > 0) {
      localStorage.setItem('viaAssessmentAnswers', JSON.stringify(answers));
    }
  }, [answers]);

  // Flatten all questions with category info
  const allQuestions = [];
  Object.entries(viaQuestions.categories).forEach(([categoryKey, category]) => {
    category.questions.forEach((question, index) => {
      allQuestions.push({
        id: `${categoryKey}_${index}`,
        text: question,
        category: categoryKey,
        categoryName: category.name
      });
    });
  });

  const totalQuestions = allQuestions.length;
  const currentQuestion = allQuestions[currentQuestionIndex];
  const answeredCount = Object.keys(answers).length;
  const isComplete = answeredCount === totalQuestions;

  useEffect(() => {
    if (isActive) {
      // Calculate scores and update parent
      const scores = calculateCategoryScores();
      onUpdate(scores, isComplete);
    }
  }, [answers, isComplete, isActive]);

  // Populate sidebar navigation when component is active
  useEffect(() => {
    if (isActive && !showQuickNavigation) {
      const container = document.getElementById('quick-navigation-container');
      const mobileContainer = document.getElementById('mobile-quick-navigation-container');

      const navigationHTML = Object.entries(viaQuestions.categories).map(([categoryKey, category]) => {
        const categoryQuestions = allQuestions.filter(q => q.category === categoryKey);
        const categoryAnswered = categoryQuestions.filter(q => answers[q.id]).length;
        const categoryProgress = (categoryAnswered / categoryQuestions.length) * 100;

        return `
          <button
            class="w-full text-left p-2 rounded-lg border border-slate-200 hover:border-slate-300 transition-all text-xs"
            onclick="window.navigateToViaCategory('${categoryKey}')"
          >
            <div class="font-medium text-slate-900 mb-1">${category.name}</div>
            <div class="text-slate-600 mb-1">
              ${categoryAnswered}/${categoryQuestions.length}
            </div>
            <div class="w-full bg-slate-200 rounded-full h-1">
              <div
                class="bg-blue-500 h-1 rounded-full transition-all"
                style="width: ${categoryProgress}%"
              ></div>
            </div>
          </button>
        `;
      }).join('');

      const mobileNavigationHTML = Object.entries(viaQuestions.categories).map(([categoryKey, category]) => {
        const categoryQuestions = allQuestions.filter(q => q.category === categoryKey);
        const categoryAnswered = categoryQuestions.filter(q => answers[q.id]).length;
        const categoryProgress = (categoryAnswered / categoryQuestions.length) * 100;

        return `
          <button
            class="w-full text-left p-3 rounded-lg border border-slate-200 hover:border-slate-300 transition-all"
            onclick="window.navigateToViaCategory('${categoryKey}')"
          >
            <div class="text-sm font-medium text-slate-900 mb-1">${category.name}</div>
            <div class="text-xs text-slate-600 mb-2">
              ${categoryAnswered}/${categoryQuestions.length} answered
            </div>
            <div class="w-full bg-slate-200 rounded-full h-1">
              <div
                class="bg-blue-500 h-1 rounded-full transition-all"
                style="width: ${categoryProgress}%"
              ></div>
            </div>
          </button>
        `;
      }).join('');

      if (container) {
        container.innerHTML = navigationHTML;
      }
      if (mobileContainer) {
        mobileContainer.innerHTML = mobileNavigationHTML;
      }

      // Set up navigation function
      window.navigateToViaCategory = (categoryKey) => {
        const firstQuestionIndex = allQuestions.findIndex(q => q.category === categoryKey);
        setCurrentQuestionIndex(firstQuestionIndex);
      };
    }
  }, [isActive, showQuickNavigation, answers, allQuestions, setCurrentQuestionIndex]);

  const calculateCategoryScores = () => {
    const categoryScores = {};
    
    Object.keys(viaQuestions.categories).forEach(categoryKey => {
      const categoryAnswers = Object.entries(answers)
        .filter(([questionId]) => questionId.startsWith(categoryKey))
        .map(([, answer]) => answer);
      
      if (categoryAnswers.length > 0) {
        const average = categoryAnswers.reduce((sum, answer) => sum + answer, 0) / categoryAnswers.length;
        // Convert from 1-5 scale to 0-100 scale
        categoryScores[categoryKey] = Math.round(((average - 1) / 4) * 100);
      } else {
        categoryScores[categoryKey] = 0;
      }
    });
    
    return categoryScores;
  };

  const handleAnswer = (value) => {
    const newAnswers = {
      ...answers,
      [currentQuestion.id]: value
    };
    setAnswers(newAnswers);
  };

  const goToNext = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };



  if (!currentQuestion) return null;

  return (
    <div id="via-assessment-container" className="p-4 md:p-5">

      {/* Question */}
      <motion.div
        id="via-assessment-question-section"
        key={currentQuestionIndex}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
        className="mb-4"
      >
        <div id="via-assessment-question-card" className="bg-slate-50 rounded-sm p-3 mb-3">
          <div id="via-assessment-question-category" className="text-xs text-slate-700 font-medium mb-1">
            {currentQuestion.categoryName}
          </div>
          <h3 id="via-assessment-question-text" className="text-sm font-medium text-slate-900 leading-snug">
            {currentQuestion.text}
          </h3>
        </div>

        {/* Answer Options - Horizontal Layout */}
        <div id="via-assessment-answer-options" className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-2">
          {viaQuestions.scale.map((option) => (
            <label
              id={`via-assessment-option-${option.value}`}
              key={option.value}
              className={`flex flex-col items-center p-3 rounded-sm border cursor-pointer transition-all text-center ${
                answers[currentQuestion.id] === option.value
                  ? 'border-slate-400 bg-slate-100'
                  : 'border-slate-200 hover:border-slate-300 bg-white'
              }`}
            >
              <input
                type="radio"
                name={`question_${currentQuestionIndex}`}
                value={option.value}
                checked={answers[currentQuestion.id] === option.value}
                onChange={() => handleAnswer(option.value)}
                className="sr-only"
              />
              <div id={`via-assessment-radio-${option.value}`} className={`w-4 h-4 rounded-sm border-2 mb-2 flex items-center justify-center ${
                answers[currentQuestion.id] === option.value
                  ? 'border-slate-700 bg-slate-700'
                  : 'border-slate-300'
              }`}>
                {answers[currentQuestion.id] === option.value && (
                  <div className="w-2 h-2 rounded-sm bg-white"></div>
                )}
              </div>
              <div id={`via-assessment-option-content-${option.value}`} className="flex flex-col items-center">
                <span className="text-xs text-slate-900 font-medium mb-1">{option.label}</span>
                <span className="text-xs text-slate-500">{option.value}</span>
              </div>
            </label>
          ))}
        </div>
      </motion.div>

      {/* Navigation */}
      <div id="via-assessment-navigation" className="flex items-center justify-between">
        <button
          id="via-assessment-previous-button"
          onClick={goToPrevious}
          disabled={currentQuestionIndex === 0}
          className={`flex items-center px-2 py-1 rounded-sm transition-all text-xs ${
            currentQuestionIndex === 0
              ? 'text-slate-400 cursor-not-allowed'
              : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
          }`}
        >
          <ChevronLeft className="w-3 h-3 mr-1" />
          Previous
        </button>

        <div id="via-assessment-progress-indicator" className="text-center">
          <div className={`inline-flex items-center px-2 py-1 rounded-sm text-xs font-medium ${
            isComplete
              ? 'bg-slate-200 text-slate-800'
              : 'bg-slate-100 text-slate-600'
          }`}>
            {isComplete ? 'Complete' : `${answeredCount}/${totalQuestions}`}
          </div>
        </div>

        <button
          id="via-assessment-next-button"
          onClick={goToNext}
          disabled={currentQuestionIndex === totalQuestions - 1}
          className={`flex items-center px-2 py-1 rounded-sm transition-all text-xs ${
            currentQuestionIndex === totalQuestions - 1
              ? 'text-slate-400 cursor-not-allowed'
              : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
          }`}
        >
          Next
          <ChevronRight className="w-3 h-3 ml-1" />
        </button>
      </div>

      {/* Quick Navigation - Only show if showQuickNavigation is true */}
      {showQuickNavigation && (
        <div id="via-assessment-quick-navigation" className="mt-8 pt-6 border-t border-slate-200">
          <h4 className="text-sm font-medium text-slate-900 mb-3">Quick Navigation by Category</h4>
          <div id="via-assessment-category-grid" className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {Object.entries(viaQuestions.categories).map(([categoryKey, category], index) => {
              const categoryQuestions = allQuestions.filter(q => q.category === categoryKey);
              const categoryAnswered = categoryQuestions.filter(q => answers[q.id]).length;
              const categoryProgress = (categoryAnswered / categoryQuestions.length) * 100;

              return (
                <button
                  id={`via-assessment-category-${categoryKey}`}
                  key={categoryKey}
                  onClick={() => {
                    const firstQuestionIndex = allQuestions.findIndex(q => q.category === categoryKey);
                    setCurrentQuestionIndex(firstQuestionIndex);
                  }}
                  className="text-left p-3 rounded-lg border border-slate-200 hover:border-slate-300 transition-all"
                >
                  <div id={`via-assessment-category-name-${categoryKey}`} className="text-sm font-medium text-slate-900 mb-1">{category.name}</div>
                  <div id={`via-assessment-category-progress-${categoryKey}`} className="text-xs text-slate-600 mb-2">
                    {categoryAnswered}/{categoryQuestions.length} answered
                  </div>
                  <div id={`via-assessment-category-bar-container-${categoryKey}`} className="w-full bg-slate-200 rounded-full h-1">
                    <div
                      id={`via-assessment-category-bar-${categoryKey}`}
                      className="bg-blue-500 h-1 rounded-full transition-all"
                      style={{ width: `${categoryProgress}%` }}
                    ></div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default ViaAssessment;
